from fastapi import APIRouter, Depends

from modules.user.application.services import UserService
from modules.user.domain.models import User, UserStatus

from .dependencies import get_user_service
from .schemas import UserPublic, UserStatusUpdate

router = APIRouter()


def user_to_public(user: User) -> UserPublic:
    """Convert User domain model to UserPublic schema."""
    return UserPublic(
        id=user.id,
        username=user.username,
        email=user.email,
        status=user.status,
    )


@router.get("/users/", response_model=list[UserPublic])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    status: UserStatus | None = None,
    user_service: UserService = Depends(get_user_service),
) -> list[UserPublic]:
    users = await user_service.list_users(skip, limit, status)
    return [user_to_public(user) for user in users]


@router.get("/users/search", response_model=list[UserPublic])
async def search_users(
    query: str,
    skip: int = 0,
    limit: int = 100,
    user_service: UserService = Depends(get_user_service),
) -> list[UserPublic]:
    users = await user_service.search_users(query, skip, limit)
    return [user_to_public(user) for user in users]


@router.get("/users/{user_id}", response_model=UserPublic)
async def get_user_by_id(
    user_id: int,
    user_service: UserService = Depends(get_user_service),
) -> UserPublic:
    user = await user_service.get_user_by_id(user_id)
    if not user:
        from fastapi import HTTPException

        raise HTTPException(status_code=404, detail="User not found")
    return user_to_public(user)


@router.patch("/users/{user_id}/status", response_model=bool)
async def update_user_status(
    user_id: int,
    status_update: UserStatusUpdate,
    user_service: UserService = Depends(get_user_service),
) -> bool:
    return await user_service.update_user_status(user_id, status_update.status)
