from fastapi import APIRouter, Depends, HTTPException, Path, Query, status

from modules.user.application.services import UserService
from modules.user.domain.models import User, UserStatus

from .dependencies import get_user_service
from .schemas import UserPublic, UserStatusUpdate

router = APIRouter(
    prefix="/users",
    tags=["users"],
    responses={
        404: {"description": "User not found"},
        422: {"description": "Validation error"},
    },
)


def user_to_public(user: User) -> UserPublic:
    """Convert User domain model to UserPublic schema."""
    return UserPublic(
        id=user.id,
        username=user.username,
        email=user.email,
        status=user.status,
    )


@router.get(
    "/",
    response_model=list[UserPublic],
    summary="List users",
    description="Retrieve a paginated list of users with optional status filtering",
    response_description="List of users matching the criteria",
)
async def list_users(
    skip: int = Query(0, ge=0, description="Number of users to skip for pagination"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of users to return"
    ),
    status: UserStatus | None = Query(None, description="Filter users by status"),
    user_service: UserService = Depends(get_user_service),
) -> list[UserPublic]:
    """
    Retrieve a paginated list of users.

    - **skip**: Number of users to skip (for pagination)
    - **limit**: Maximum number of users to return (1-1000)
    - **status**: Optional status filter (active, inactive, suspended)
    """
    users = await user_service.list_users(skip, limit, status)
    return [user_to_public(user) for user in users]


@router.get(
    "/search",
    response_model=list[UserPublic],
    summary="Search users",
    description="Search users by username or email with pagination",
    response_description="List of users matching the search query",
)
async def search_users(
    query: str = Query(
        ..., min_length=1, description="Search query for username or email"
    ),
    skip: int = Query(0, ge=0, description="Number of users to skip for pagination"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of users to return"
    ),
    user_service: UserService = Depends(get_user_service),
) -> list[UserPublic]:
    """
    Search users by username or email.

    - **query**: Search term to match against username or email (case-insensitive)
    - **skip**: Number of users to skip (for pagination)
    - **limit**: Maximum number of users to return (1-1000)
    """
    users = await user_service.search_users(query, skip, limit)
    return [user_to_public(user) for user in users]


@router.get(
    "/{user_id}",
    response_model=UserPublic,
    summary="Get user by ID",
    description="Retrieve a specific user by their unique identifier",
    response_description="User details",
    responses={
        200: {"description": "User found and returned"},
        404: {"description": "User not found"},
    },
)
async def get_user_by_id(
    user_id: int = Path(..., gt=0, description="Unique identifier of the user"),
    user_service: UserService = Depends(get_user_service),
) -> UserPublic:
    """
    Retrieve a specific user by their ID.

    - **user_id**: The unique identifier of the user (must be positive integer)
    """
    user = await user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with ID {user_id} not found",
        )
    return user_to_public(user)


@router.patch(
    "/{user_id}/status",
    response_model=bool,
    summary="Update user status",
    description="Update the status of a specific user",
    response_description="True if status was updated successfully, False if user not found",
    responses={
        200: {"description": "Status update result"},
        404: {"description": "User not found"},
        422: {"description": "Invalid status value"},
    },
)
async def update_user_status(
    user_id: int = Path(..., gt=0, description="Unique identifier of the user"),
    status_update: UserStatusUpdate = ...,
    user_service: UserService = Depends(get_user_service),
) -> bool:
    """
    Update the status of a specific user.

    - **user_id**: The unique identifier of the user (must be positive integer)
    - **status_update**: New status information

    Returns True if the user was found and updated, False if user was not found.
    """
    return await user_service.update_user_status(user_id, status_update.status)
