import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session
from modules.user.domain.models import UserStatus
from modules.user.infrastructure.orm import UserDB
from main import app
from common.db.database import SessionLocal

@pytest.fixture(scope="module")
def test_client():
    return TestClient(app)

@pytest.fixture
def db():
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()

@pytest.fixture
def test_user(db: Session):
    """Create a test user and return it"""
    user = UserDB(
        username="testuser_api",
        email="<EMAIL>",
        hashed_password="hashed_password",
        status=UserStatus.ACTIVE
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    yield user
    db.delete(user)
    db.commit()

def should_return_list_of_users_when_valid_request(test_client, test_user):
    response = test_client.get("/api/v1/users/")
    assert response.status_code == 200
    users = response.json()
    assert isinstance(users, list)
    assert any(user["username"] == "testuser_api" for user in users)

def should_return_filtered_users_when_status_provided(test_client, test_user):
    response = test_client.get("/api/v1/users/?status=active")
    assert response.status_code == 200
    users = response.json()
    assert all(user["status"] == "active" for user in users)

def should_find_users_when_search_query_matches(test_client, test_user):
    response = test_client.get("/api/v1/users/search?query=testuser_api")
    assert response.status_code == 200
    users = response.json()
    assert len(users) > 0
    assert users[0]["username"] == "testuser_api"

def should_return_empty_list_when_search_query_does_not_match(test_client):
    response = test_client.get("/api/v1/users/search?query=non_existing_user")
    assert response.status_code == 200
    users = response.json()
    assert len(users) == 0

def should_update_user_status_when_valid_data(test_client, test_user, db: Session):
    # Print initial status
    initial_db_user = db.query(UserDB).filter(UserDB.id == test_user.id).first()
    print(f"Initial DB status: {initial_db_user.status}")
    
    update_data = {"status": "inactive"}
    response = test_client.patch(
        f"/api/v1/users/{test_user.id}/status",
        json=update_data
    )
    assert response.status_code == 200
    assert response.json() is True

    # Verify the update in database
    updated_db_user = db.query(UserDB).filter(UserDB.id == test_user.id).first()
    print(f"Updated DB status: {updated_db_user.status}")
    assert updated_db_user.status == "inactive", f"Expected 'inactive' but got '{updated_db_user.status}'"

    # Verify the update via API
    response = test_client.get(f"/api/v1/users/?id={test_user.id}")
    user = response.json()[0]
    print(f"API response status: {user['status']}")
    assert user["status"] == "inactive", f"Expected 'inactive' but got '{user['status']}'"

def should_return_false_when_updating_non_existing_user(test_client):
    update_data = {"status": "inactive"}
    response = test_client.patch(
        "/api/v1/users/9999/status",
        json=update_data
    )
    assert response.status_code == 200
    assert response.json() is False

def should_return_422_when_invalid_status_provided(test_client, test_user):
    update_data = {"status": "INVALID_STATUS"}
    response = test_client.patch(
        f"/api/v1/users/{test_user.id}/status",
        json=update_data
    )
    assert response.status_code == 422