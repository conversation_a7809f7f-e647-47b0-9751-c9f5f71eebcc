from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from modules.auth.application.services import AuthService
from modules.auth.interfaces.dependencies import get_auth_service, get_current_user
from modules.auth.interfaces.schemas import (
    OAuthCallbackRequest,
    Token,
)
from modules.user.domain.models import User
from modules.user.interfaces.schemas import (
    UserCreate,
    UserPublic,
)

router = APIRouter(
    prefix="/auth",
    tags=["authentication"],
    responses={
        401: {"description": "Authentication failed"},
        422: {"description": "Validation error"},
    },
)


@router.post(
    "/register",
    response_model=UserPublic,
    status_code=status.HTTP_201_CREATED,
    summary="Register new user",
    description="Create a new user account with username, email and password",
    response_description="Successfully created user information",
    responses={
        201: {"description": "User successfully created"},
        409: {"description": "Username or email already exists"},
        422: {"description": "Invalid input data"},
    },
)
async def register(
    user_data: UserCreate = Body(..., description="User registration data"),
    auth_service: AuthService = Depends(get_auth_service),
) -> User:
    """
    Register a new user account.

    - **username**: Unique username (3-50 characters)
    - **email**: Valid email address
    - **password**: Strong password (minimum 8 characters)

    Returns the created user information (without password).
    """
    try:
        user = await auth_service.register_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password,
        )
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Registration failed: {str(e)}",
        ) from e


@router.post(
    "/login",
    response_model=Token,
    summary="User login",
    description="Authenticate user with username/email and password to get access token",
    response_description="JWT access token for authenticated requests",
    responses={
        200: {"description": "Login successful, access token returned"},
        401: {"description": "Invalid username/email or password"},
        422: {"description": "Invalid request format"},
    },
)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthService = Depends(get_auth_service),
) -> Token:
    """
    Authenticate user and return access token.

    - **username**: Username or email address
    - **password**: User password

    Returns a JWT access token that should be included in the Authorization header
    for protected endpoints as: `Authorization: Bearer <token>`
    """
    user = await auth_service.authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username/email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = auth_service.create_access_token(user)
    return Token(access_token=access_token, token_type="bearer")  # noqa: S106


@router.post(
    "/oauth/callback",
    response_model=Token,
    summary="OAuth callback handler",
    description="Process OAuth provider callback and return access token",
    response_description="JWT access token for the authenticated user",
    responses={
        200: {"description": "OAuth authentication successful"},
        400: {"description": "Invalid OAuth callback data"},
        500: {"description": "OAuth processing failed"},
    },
)
async def oauth_callback(
    oauth_data: OAuthCallbackRequest = Body(
        ..., description="OAuth callback data from provider"
    ),
    auth_service: AuthService = Depends(get_auth_service),
) -> Token:
    """
    Handle OAuth provider callback and authenticate user.

    - **provider**: OAuth provider name (e.g., 'google', 'github')
    - **oauth_id**: Unique user ID from OAuth provider
    - **email**: User email from OAuth provider
    - **name**: Optional user display name from OAuth provider

    Creates a new user account if the email doesn't exist, or links the OAuth
    account to an existing user. Returns a JWT access token.
    """
    try:
        _, access_token = await auth_service.handle_oauth_callback(
            provider=oauth_data.provider,
            oauth_id=oauth_data.oauth_id,
            email=oauth_data.email,
            _name=oauth_data.name,
        )
        return Token(access_token=access_token, token_type="bearer")  # noqa: S106
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid OAuth data: {str(e)}",
        ) from e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"OAuth processing failed: {str(e)}",
        ) from e


@router.get(
    "/protected",
    summary="Protected route example",
    description="Example protected endpoint that requires valid JWT authentication",
    response_description="Success message with current user information",
    responses={
        200: {"description": "Access granted, user information returned"},
        401: {"description": "Unauthorized - Invalid or missing token"},
    },
)
async def protected_route(
    current_user: User = Depends(get_current_user),
) -> dict[str, str | dict[str, str | int]]:
    """
    Example protected route that requires authentication.

    This endpoint demonstrates how to protect routes using JWT authentication.
    Include the access token in the Authorization header:
    `Authorization: Bearer <your_access_token>`

    Returns the current user's information if authentication is successful.
    """
    return {
        "message": "Access granted to protected route",
        "user": {
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email,
        },
    }
