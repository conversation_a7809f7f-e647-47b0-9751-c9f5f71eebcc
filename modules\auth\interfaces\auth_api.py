from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from modules.auth.application.services import AuthService
from modules.auth.interfaces.dependencies import get_auth_service, get_current_user
from modules.auth.interfaces.schemas import (
    OAuthCallbackRequest,
    Token,
    UserCreate,
    UserPublic,
)
from modules.user.domain.models import User

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post(
    "/register",
    response_model=UserPublic,
    status_code=status.HTTP_201_CREATED,
    responses={409: {"description": "用户名或邮箱已存在"}},
    summary="注册新用户",
    description="创建新用户账户，需要提供用户名、邮箱和密码",
)
async def register(
    user_data: UserCreate, auth_service: AuthService = Depends(get_auth_service)
) -> User:
    try:
        user = await auth_service.register_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password,
        )
        return user
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e)) from e


@router.post(
    "/login",
    response_model=Token,
    responses={401: {"description": "无效的用户名或密码"}},
    summary="用户登录",
    description="使用用户名和密码获取访问令牌",
)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthService = Depends(get_auth_service),
) -> Token:
    user = await auth_service.authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = auth_service.create_access_token(user)
    return Token(access_token=access_token, token_type="bearer")  # noqa: S106


@router.post(
    "/oauth/callback",
    response_model=Token,
    responses={
        400: {"description": "OAuth处理失败"},
        500: {"description": "服务器内部错误"},
    },
    summary="OAuth回调处理",
    description="处理OAuth提供商的回调请求并返回用户信息及访问令牌",
)
async def oauth_callback(
    oauth_data: OAuthCallbackRequest,
    auth_service: AuthService = Depends(get_auth_service),
) -> Token:
    try:
        user, access_token = await auth_service.handle_oauth_callback(
            provider=oauth_data.provider,
            oauth_id=oauth_data.oauth_id,
            email=oauth_data.email,
            _name=oauth_data.name,
        )
        return Token(access_token=access_token, token_type="bearer")  # noqa: S106
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"OAuth processing failed: {str(e)}",
        ) from e


@router.get(
    "/protected",
    responses={401: {"description": "Unauthorized - Invalid or missing token"}},
    summary="Protected route for testing",
    description="A protected route that requires valid JWT token for access",
)
async def protected_route(
    current_user: User = Depends(get_current_user),
) -> dict[str, str | dict[str, str | int]]:
    """Protected route that requires authentication"""
    return {
        "message": "Access granted to protected route",
        "user": {
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email,
        },
    }
