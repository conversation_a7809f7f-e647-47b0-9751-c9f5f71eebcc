import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from passlib.context import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy.orm import Session

from common.db.database import SessionLocal
from main import app
from modules.user.domain.models import UserStatus
from modules.user.infrastructure.orm import UserDB


@pytest.fixture(scope="module")
def test_client():
    """Create a test client for auth module tests"""
    return TestClient(app)


@pytest.fixture(scope="module")
def db():
    """Create a database session for auth module tests"""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture(scope="module")
def auth_test_user(db: Session):
    """Create a test user specifically for auth tests"""
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    hashed_password = pwd_context.hash("testpassword")
    
    user = UserDB(
        username="auth_testuser",
        email="<EMAIL>",
        hashed_password=hashed_password,
        status=UserStatus.ACTIVE
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    
    yield user
    
    # Cleanup after all auth tests are done
    db.delete(user)
    db.commit()


@pytest.fixture(scope="module")
def auth_credentials(auth_test_user):
    """Provide auth credentials for testing"""
    return {
        "username": auth_test_user.username,
        "password": "testpassword"
    }
