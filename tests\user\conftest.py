import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from common.db.database import SessionLocal
from main import app
from modules.user.domain.models import UserStatus
from modules.user.infrastructure.orm import UserDB


@pytest.fixture(scope="module")
def test_client():
    """Create a test client for user module tests"""
    return TestClient(app)


@pytest.fixture
def db():
    """Create a database session for user module tests"""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def test_user(db: Session):
    """Create a test user for individual test cases"""
    user = UserDB(
        username="testuser_api",
        email="<EMAIL>",
        hashed_password="hashed_password",
        status=UserStatus.ACTIVE
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    yield user
    db.delete(user)
    db.commit()
