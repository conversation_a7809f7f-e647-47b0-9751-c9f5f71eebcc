import os

import pytest
from dotenv import load_dotenv
from passlib.context import Crypt<PERSON>ontext

from common.db.database import SessionLocal
from modules.user.domain.models import UserStatus
from modules.user.infrastructure.orm import UserDB


# 加载测试环境变量
def pytest_configure() -> None:
    # 从项目根目录加载.env文件
    dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
    load_dotenv(dotenv_path)


@pytest.fixture(scope="session", autouse=True)
def setup_test_data() -> None:
    """Setup test data for all tests"""
    db = SessionLocal()
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    try:
        # Check if test user already exists
        existing_user = db.query(UserDB).filter(UserDB.username == "testuser").first()
        if existing_user:
            # Update existing user's password to ensure it's correct
            existing_user.hashed_password = pwd_context.hash("testpassword")
            db.commit()
        else:
            # Create test user
            hashed_password = pwd_context.hash("testpassword")
            test_user = UserDB(
                username="testuser",
                email="<EMAIL>",
                hashed_password=hashed_password,
                status=UserStatus.ACTIVE,
            )
            db.add(test_user)
            db.commit()
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()
